package com.score.callmetest.db.repository

import com.score.callmetest.db.dao.ChatMessageDao
import com.score.callmetest.db.entity.ChatMessageRoomEntity
import com.score.callmetest.entity.ChatMessageEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

/**
 * 聊天消息仓库，负责处理聊天消息相关的数据操作
 */
class ChatMessageRepository(private val chatMessageDao: ChatMessageDao) {
    /**
     * 插入一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    suspend fun insertChatMessage(chatMessage: ChatMessageEntity) = withContext(Dispatchers.IO) {
        chatMessageDao.insertChatMessage(ChatMessageRoomEntity.fromEntity(chatMessage))
    }
    
    /**
     * 批量插入聊天消息
     * @param chatMessages 聊天消息实体列表
     */
    suspend fun insertChatMessages(chatMessages: List<ChatMessageEntity>) = withContext(Dispatchers.IO) {
        chatMessageDao.insertChatMessages(chatMessages.map { ChatMessageRoomEntity.fromEntity(it) })
    }
    
    /**
     * 更新一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    suspend fun updateChatMessage(chatMessage: ChatMessageEntity) = withContext(Dispatchers.IO) {
        chatMessageDao.updateChatMessage(ChatMessageRoomEntity.fromEntity(chatMessage))
    }
    
    /**
     * 删除一条聊天消息
     * @param chatMessage 聊天消息实体
     */
    suspend fun deleteChatMessage(chatMessage: ChatMessageEntity) = withContext(Dispatchers.IO) {
        chatMessageDao.deleteChatMessage(ChatMessageRoomEntity.fromEntity(chatMessage))
    }
    
    /**
     * 根据消息ID删除一条聊天消息
     * @param messageId 消息ID
     */
    suspend fun deleteChatMessageById(messageId: String) = withContext(Dispatchers.IO) {
        chatMessageDao.deleteChatMessageById(messageId)
    }
    
    /**
     * 获取所有聊天消息
     * @return 聊天消息流
     */
    fun getAllChatMessages(): Flow<List<ChatMessageEntity>> {
        return chatMessageDao.getAllChatMessages().map { roomEntities ->
            roomEntities.map { it.toEntity() }
        }
    }

    /**
     * 获取特定用户的所有聊天消息，按时间排序
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     * @return 聊天消息流
     */
    fun getChatMessagesByUserId(currentUserId: String, userId: String): Flow<List<ChatMessageEntity>> {
        return chatMessageDao.getChatMessagesByUserId(currentUserId, userId).map { roomEntities ->
            roomEntities.map { it.toEntity() }
        }
    }

    /**
     * 根据消息ID获取一条聊天消息
     * @param messageId 消息ID
     * @return 聊天消息实体
     */
    suspend fun getChatMessageById(messageId: String): ChatMessageEntity? = withContext(Dispatchers.IO) {
        chatMessageDao.getChatMessageById(messageId)?.toEntity()
    }

    /**
     * 更新状态
     * @param messageId 消息ID
     * @param status 状态
     */
    suspend fun updateSendStatus(messageId: String, status: String) = withContext(Dispatchers.IO) {
        chatMessageDao.updateSendStatus(messageId,status)
    }

    /**
     * 清空所有聊天消息
     */
    suspend fun clearAllChatMessages() = withContext(Dispatchers.IO) {
        chatMessageDao.clearAllChatMessages()
    }

    /**
     * 清空当前用户与特定用户之间的所有聊天消息
     * @param currentUserId 当前用户ID
     * @param userId 用户ID
     */
    suspend fun clearChatMessagesByUserId(currentUserId: String, userId: String) = withContext(Dispatchers.IO) {
        chatMessageDao.clearChatMessagesByUserId(currentUserId, userId)
    }
}