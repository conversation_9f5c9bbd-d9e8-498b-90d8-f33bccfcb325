package com.score.callmetest.ui.videocall.ongoing.adapter

import android.graphics.Color
import android.text.SpannableString
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.graphics.ColorUtils
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemVideoMsgBinding
import com.score.callmetest.entity.VideoCallMessageEntity
import com.score.callmetest.manager.TranslateManager
import com.score.callmetest.network.UserInfo
import com.score.callmetest.util.LanguageUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import timber.log.Timber

/**
 * 视频通话消息适配器
 * 用于展示视频通话过程中的聊天消息
 * 
 * 特点：
 * 1. 使用同一个布局 item_video_msg.xml
 * 2. 只有对方发送的消息才显示 group_bottom 内容
 * 3. 支持翻译内容的展示
 * 4. 内存优化和性能考虑
 */
class VideoCallMessageAdapter(
    private val onMessageClickListener: ((VideoCallMessageEntity) -> Unit)? = null,
    private var targetUserInfo: UserInfo? = null
) : ListAdapter<VideoCallMessageEntity, VideoCallMessageAdapter.MessageViewHolder>(MessageDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MessageViewHolder {
        val binding = ItemVideoMsgBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return MessageViewHolder(binding, onMessageClickListener, this)
    }

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * 更新对方用户信息
     * 用于获取对方的语言信息以进行自动翻译判断
     */
    fun updateTargetUserInfo(userInfo: UserInfo?) {
        this.targetUserInfo = userInfo
    }

    /**
     * 消息ViewHolder
     */
    class MessageViewHolder(
        val binding: ItemVideoMsgBinding,
        private val onMessageClickListener: ((VideoCallMessageEntity) -> Unit)?,
        private val adapter: VideoCallMessageAdapter
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(message: VideoCallMessageEntity) {
            // 设置主要消息内容 - 使用SpannableString格式化颜色
            binding.tvMessage.text = createFormattedMessage(message.senderName, message.content)

            // 检查是否需要自动翻译（只翻译对方的消息）
            if (!message.isCurrentUser && message.translateContent.isNullOrEmpty()) {
                checkAndAutoTranslateInline(message)
            }

            // 根据是否是对方发送的消息来决定是否显示底部内容
            if (message.shouldShowBottomContent()) {
                // 显示分割线和翻译内容
                binding.groupBottom.visibility = View.VISIBLE
                binding.divider.visibility = View.VISIBLE
                binding.tvMessageTranslate.visibility = View.VISIBLE
                // 翻译内容使用普通文本显示
                binding.tvMessageTranslate.text = message.getDisplayTranslateContent()
            } else {
                // 隐藏底部内容
                binding.groupBottom.visibility = View.GONE
                binding.divider.visibility = View.GONE
                binding.tvMessageTranslate.visibility = View.GONE
            }

            // 设置点击事件
            binding.root.setOnClickListener {
                onMessageClickListener?.invoke(message)
            }
        }

        /**
         * 内联的自动翻译检查方法
         */
        private fun checkAndAutoTranslateInline(message: VideoCallMessageEntity) {
            val context = binding.root.context

            // 检查自动翻译开关
            val autoTranslateEnabled = SharePreferenceUtil.getBoolean(
                Constant.AUTO_TRANSLATE,
                true,
                "settings"
            )

            if (!autoTranslateEnabled) {
                Timber.d("自动翻译未开启")
                return
            }

            // 获取用户设置的语言
            LanguageUtils.getAppLanguage(context) { userLanguage ->
                // 获取对方的语言（从外部适配器的targetUserInfo中获取）
                val targetLanguage = adapter.targetUserInfo?.language

                if (targetLanguage.isNullOrEmpty()) {
                    Timber.d("对方语言信息为空，跳过翻译")
                    return@getAppLanguage
                }

                // 检查语言是否一致
                if (userLanguage.equals(targetLanguage, ignoreCase = true)) {
                    Timber.d("语言一致($userLanguage)，无需翻译")
                    return@getAppLanguage
                }

                Timber.d("开始自动翻译: $targetLanguage -> $userLanguage")

                // 执行翻译
                TranslateManager.translate(message.content, userLanguage) { translatedText ->
                    ThreadUtils.runOnMain {
                        if (!translatedText.isNullOrEmpty()) {
                            // 翻译成功，更新消息实体
                            message.translateContent = translatedText
                            message.showTranslation = true

                            // 通知适配器更新这个item
                            val position = adapter.currentList.indexOf(message)
                            if (position != -1) {
                                adapter.notifyItemChanged(position, PAYLOAD_TRANSLATION_CHANGED)
                            }

                            Timber.d("自动翻译成功: ${message.content} -> $translatedText")
                        } else {
                            Timber.d("自动翻译失败")
                        }
                    }
                }
            }
        }

        /**
         * 创建格式化的消息内容
         * 用户名和冒号颜色：video_msg_item_name，消息内容颜色：WHITE
         *
         * @param senderName 发送者名称
         * @param content 消息内容
         * @return 格式化的SpannableString
         */
        private fun createFormattedMessage(senderName: String, content: String): SpannableString {
            val fullText = "$senderName: $content"
            val spannableString = SpannableString(fullText)

            try {
                // 用户名和冒号颜色 video_msg_item_name
                val nameColor = ContextCompat.getColor(binding.root.context, R.color.video_msg_item_name)
                val nameAndColonEndIndex = senderName.length + 1 // 包含冒号
                spannableString.setSpan(
                    ForegroundColorSpan(nameColor),
                    0,
                    nameAndColonEndIndex,
                    SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                )

                // 消息内容颜色 WHITE (冒号后面的空格和内容)
                val contentColor = Color.WHITE
                if (nameAndColonEndIndex < fullText.length) {
                    spannableString.setSpan(
                        ForegroundColorSpan(contentColor),
                        nameAndColonEndIndex,
                        fullText.length,
                        SpannableString.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                }
            } catch (e: Exception) {
                // 如果颜色解析失败，记录日志但不影响功能
                Timber.e(e, "Failed to parse color for message formatting")
            }


            return spannableString
        }


    }



    /**
     * 消息差异比较回调
     * 用于优化RecyclerView的性能，只更新有变化的item
     */
    private class MessageDiffCallback : DiffUtil.ItemCallback<VideoCallMessageEntity>() {
        override fun areItemsTheSame(
            oldItem: VideoCallMessageEntity,
            newItem: VideoCallMessageEntity
        ): Boolean {
            return oldItem.messageId == newItem.messageId
        }

        override fun areContentsTheSame(
            oldItem: VideoCallMessageEntity,
            newItem: VideoCallMessageEntity
        ): Boolean {
            return oldItem == newItem
        }

        override fun getChangePayload(
            oldItem: VideoCallMessageEntity,
            newItem: VideoCallMessageEntity
        ): Any? {
            // 如果只是翻译状态发生变化，返回特定的payload
            if (oldItem.copy(showTranslation = newItem.showTranslation, translateContent = newItem.translateContent) == newItem) {
                return PAYLOAD_TRANSLATION_CHANGED
            }
            return null
        }
    }

    override fun onBindViewHolder(holder: MessageViewHolder, position: Int, payloads: List<Any>) {
        if (payloads.isNotEmpty() && payloads.contains(PAYLOAD_TRANSLATION_CHANGED)) {
            // 只更新翻译相关的UI
            val message = getItem(position)
            if (message.shouldShowBottomContent()) {
                holder.binding.groupBottom.visibility = View.VISIBLE
                holder.binding.divider.visibility = View.VISIBLE
                holder.binding.tvMessageTranslate.visibility = View.VISIBLE
                holder.binding.tvMessageTranslate.text = message.getDisplayTranslateContent()
            } else {
                holder.binding.groupBottom.visibility = View.GONE
                holder.binding.divider.visibility = View.GONE
                holder.binding.tvMessageTranslate.visibility = View.GONE
            }
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    companion object {
        private const val PAYLOAD_TRANSLATION_CHANGED = "translation_changed"
    }
}

/**
 * 消息监听器接口
 * 预留扩展接口，便于后续功能扩展
 */
interface VideoCallMessageListener {
    /**
     * 消息点击事件
     */
    fun onMessageClick(message: VideoCallMessageEntity)
    
    /**
     * 消息长按事件
     */
    fun onMessageLongClick(message: VideoCallMessageEntity): Boolean
    
    /**
     * 翻译按钮点击事件
     */
    fun onTranslateClick(message: VideoCallMessageEntity)
    
    /**
     * 重发消息事件
     */
    fun onResendMessage(message: VideoCallMessageEntity)
}
