package com.score.callmetest.ui.mine

import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.score.callmetest.BuildConfig
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivitySettingsBinding
import com.score.callmetest.db.AppDatabase
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import com.score.callmetest.ui.login.LoginActivity
import com.score.callmetest.ui.mine.about.AboutUsActivity
import com.score.callmetest.ui.mine.blockList.BlockListContainerFragment
import com.score.callmetest.ui.widget.BaseCustomDialog
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import androidx.core.net.toUri
import com.score.callmetest.CallmeApplication.Companion.context

class SettingsActivity : BaseActivity<ActivitySettingsBinding, EmptyViewModel>() {
    override fun getViewBinding(): ActivitySettingsBinding {
        return ActivitySettingsBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    private var originDisturbCall = false
    private var originDisturbIm = false

    override fun initView() {
        originDisturbCall = UserInfoManager.myUserInfo?.isSwitchNotDisturbCall ?: false
        originDisturbIm = UserInfoManager.myUserInfo?.isSwitchNotDisturbIm ?: false

        // Switch 状态初始化
        binding.switchAutoTranslate.isChecked = getSetting(Constant.AUTO_TRANSLATE, true)
        binding.switchDndCall.isChecked = originDisturbCall
        binding.switchDndMsg.isChecked = originDisturbIm
        binding.tvVersion.text = BuildConfig.VERSION_NAME

    }

    override fun initListener() {
        // 返回按钮
        binding.ivBack.click { finish() }

        // SwitchAutoTranslate 监听
        binding.switchAutoTranslate.setOnCheckedChangeListener { _, isChecked ->
            putSetting(Constant.AUTO_TRANSLATE, isChecked)
        }

        // SwitchDndCall 监听
        binding.switchDndCall.setOnCheckedChangeListener { _, isChecked ->
            if (!isChecked) {
                UserInfoManager.myUserInfo?.isSwitchNotDisturbCall = false
            } else {
                BaseCustomDialog(
                    context = this,
                    emojiResId = R.drawable.emoji_call,
                    title = "You will not receive calls from the others. Are you sure to turn it on?",
                    agreeText = "Confirm",
                    cancelText = "Cancel",
                    onAgree = {
                        UserInfoManager.myUserInfo?.isSwitchNotDisturbCall = true
                    },
                    onCancel = {
                        binding.switchDndCall.isChecked = false
                        UserInfoManager.myUserInfo?.isSwitchNotDisturbCall = false
                    }
                ).show()
            }
        }

        // SwitchDndMsg 监听
        binding.switchDndMsg.setOnCheckedChangeListener { _, isChecked ->
            if (!isChecked) {
                UserInfoManager.myUserInfo?.isSwitchNotDisturbIm = false
            } else {
                BaseCustomDialog(
                    context = this,
                    emojiResId = R.drawable.emoji_msg,
                    title = "You will not receive messages from strangers. Are you sure to turn it on?",
                    agreeText = "Confirm",
                    cancelText = "Cancel",
                    onAgree = {
                        UserInfoManager.myUserInfo?.isSwitchNotDisturbIm = true
                    },
                    onCancel = {
                        binding.switchDndMsg.isChecked = false
                        UserInfoManager.myUserInfo?.isSwitchNotDisturbIm = false
                    }
                ).show()
            }
        }

        // Blocklist 点击事件
        binding.itemBlocklist.click {
            val fragment = BlockListContainerFragment()
            supportFragmentManager.beginTransaction()
                .add(android.R.id.content, fragment)
                .addToBackStack(null)
                .commit()
        }

        // About Us 点击事件
        binding.itemAboutUs.click {
            val intent = Intent(this, AboutUsActivity::class.java)
            startActivity(intent)
        }

        // delete Account 点击事件
        binding.itemDeleteAccount.click {
            BaseCustomDialog(
                context = this,
                emojiResId = R.drawable.emoji_cry,
                title = "Are you sure to delete your account?",
                content = null,
                onAgree = {
                    SharePreferenceUtil.putBoolean(Constant.KEY_IS_FIRST_LOGIN, true)
                    // 调用logout接口
                    lifecycleScope.launch {
                        try {
                            val response = RetrofitUtils.dataRepository.deleteAccount()
                            if (response is NetworkResult.Success) {
                                CustomUtils.logoutAndClearData(1,this@SettingsActivity) // 退出登录时清理所有本地数据、SDK资源，并跳转到登录页
                            }
                        } catch (e: Exception) {
                            Timber.e(e)
                        }
                    }

                    ToastUtils.showToast(getString(R.string.delete_account))
                },
                onCancel = {}
            ).show()
        }


        // Google 点击事件
        binding.itemGoogle.click {
            val intent = Intent(Intent.ACTION_VIEW, "https://www.google.com".toUri())
            startActivity(intent)
        }

        // Gmail 点击事件
        binding.itemGmail.click {
            val intent = Intent(Intent.ACTION_SENDTO, "mailto:".toUri())
            intent.putExtra(Intent.EXTRA_EMAIL, arrayOf("<EMAIL>"))
            startActivity(intent)
        }

        // Log out 点击事件
        binding.itemLogout.click {
            BaseCustomDialog(
                context = this,
                emojiResId = R.drawable.emoji_cry,
                title = "Are you sure to log out?",
                content = null,
                onAgree = {
                    SharePreferenceUtil.putBoolean(Constant.KEY_IS_FIRST_LOGIN, true)
                    // 调用logout接口
                    lifecycleScope.launch {
                        try {
                            val response = RetrofitUtils.dataRepository.logout()
                            if (response is NetworkResult.Success) {
                                CustomUtils.logoutAndClearData(context = this@SettingsActivity) // 退出登录时清理所有本地数据、SDK资源，并跳转到登录页
                            }
                        } catch (e: Exception) {
                            Timber.e(e)
                        }
                    }
                    ToastUtils.showToast(context.getString(R.string.logout))
                },
                onCancel = {}
            ).show()
        }
    }

    private fun putSetting(key: String, value: Boolean) {
        SharePreferenceUtil.putBoolean(key, value, "settings")
    }

    private fun getSetting(key: String, def: Boolean = false): Boolean {
        return SharePreferenceUtil.getBoolean(key, def, "settings")
    }

    override fun onDestroy() {
        if (originDisturbIm != binding.switchDndMsg.isChecked
            || originDisturbCall != binding.switchDndCall.isChecked) {
            // 与初始状态不一致，则更新接口，不使用lifecycleScope
            UserInfoManager.updateDisturbState(
                scope = CoroutineScope(Dispatchers.IO),
                isSwitchNotDisturbIm = binding.switchDndMsg.isChecked,
                isSwitchNotDisturbCall = binding.switchDndCall.isChecked
            ) {
                UserInfoManager.refreshMyUserInfo()
            }
        }

        super.onDestroy()
    }
}